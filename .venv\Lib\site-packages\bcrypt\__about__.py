# Author:: <PERSON> (<<EMAIL>>)
# Copyright:: Copyright (c) 2013 <PERSON>
# License:: Apache License, Version 2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from __future__ import absolute_import
from __future__ import division
from __future__ import unicode_literals

__all__ = [
    "__title__",
    "__summary__",
    "__uri__",
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "__copyright__",
]

__title__ = "bcrypt"
__summary__ = "Modern password hashing for your software and your servers"
__uri__ = "https://github.com/pyca/bcrypt/"

__version__ = "3.2.0"

__author__ = "The Python Cryptographic Authority developers"
__email__ = "<EMAIL>"

__license__ = "Apache License, Version 2.0"
__copyright__ = "Copyright 2013-2020 {0}".format(__author__)
