from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from .. import crud, models, schemas
from ..database import SessionLocal
from datetime import datetime, timedelta
from ..crud import get_user_by_username, verify_password

router = APIRouter()

# Dependency to get the DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/token", response_model=schemas.AuthResponse)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = get_user_by_username(db, form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Placeholder for user details - populate from the fetched user object
    user_info = schemas.UserInfo(
        id=str(user.id),
        username=str(user.username),
        email=str(user.email),
        role=user.role.value,
        departmentId=str(user.department_id),
        branchId=str(user.branch_id)
    )
    
    access_token_expires = timedelta(minutes=30)
    expires_at = datetime.utcnow() + access_token_expires

    return schemas.AuthResponse(
        accessToken="fake-access-token", # Replace with actual JWT token generation
        tokenType="bearer",
        userInfo=user_info,
        expiresIn=int(access_token_expires.total_seconds()),
        issuedAt=datetime.utcnow()
    )