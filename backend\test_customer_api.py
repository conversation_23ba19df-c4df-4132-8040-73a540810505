import requests
import json
import uuid
from datetime import datetime, date

BASE_URL = "http://127.0.0.1:8000/api"

def create_customer():
    customer_id = str(uuid.uuid4())
    customer_data = {
        "id": customer_id,
        "fullNameKhmer": "<PERSON>",
        "fullNameLatin": "<PERSON>",
        "dateOfBirth": "1990-01-01T00:00:00Z",
        "idNumber": "123456789",
        "idCardType": "nid",
        "idCardImages": ["image1.jpg", "image2.jpg"],
        "requestedAmount": 1000.0,
        "loanPurposes": ["family"],
        "purposeDetails": "Family expenses",
        "productType": "monthly",
        "desiredLoanTerm": "12 months",
        "requestedDisbursementDate": "2025-07-22T00:00:00Z",
        "loanStatus": "draft",
        "interestRate": 0.05,
        "loanAmount": 1000.0,
        "loanStartDate": "2025-07-22",
        "loanEndDate": "2026-07-22",
        "collaterals": [{"type": "car", "value": "10000"}],
        "documents": [{"type": "agreement", "url": "agreement.pdf"}],
        "selectedCollateralTypes": ["car"],
    }
    print(f"Creating customer with ID: {customer_id}")
    response = requests.post(f"{BASE_URL}/customers/", json=customer_data)
    print(f"Create Customer Response Status: {response.status_code}")
    print(f"Create Customer Response Body: {response.json()}")
    return customer_id, response.json()

def get_customer(customer_id):
    print(f"\nFetching customer with ID: {customer_id}")
    response = requests.get(f"{BASE_URL}/customers/{customer_id}")
    print(f"Get Customer Response Status: {response.status_code}")
    print(f"Get Customer Response Body: {response.json()}")
    return response.json()

def update_customer_incorrect_version(customer_id, current_version):
    customer_data = {
        "id": customer_id,
        "fullNameKhmer": "Jane Doe",
        "version": current_version + 1,  # Incorrect version
    }
    print(f"\nUpdating customer {customer_id} with INCORRECT version {customer_data['version']}")
    response = requests.put(f"{BASE_URL}/customers/{customer_id}", json=customer_data)
    print(f"Update Customer (Incorrect Version) Response Status: {response.status_code}")
    print(f"Update Customer (Incorrect Version) Response Body: {response.json()}")
    return response.json()

def update_customer_correct_version(customer_id, current_version):
    customer_data = {
        "id": customer_id,
        "fullNameKhmer": "Jane Doe Updated",
        "version": current_version,  # Correct version
    }
    print(f"\nUpdating customer {customer_id} with CORRECT version {customer_data['version']}")
    response = requests.put(f"{BASE_URL}/customers/{customer_id}", json=customer_data)
    print(f"Update Customer (Correct Version) Response Status: {response.status_code}")
    print(f"Update Customer (Correct Version) Response Body: {response.json()}")
    return response.json()

def login_user(username, password):
    login_data = {
        "username": username,
        "password": password
    }
    print(f"\nAttempting to log in user: {username}")
    response = requests.post(f"{BASE_URL}/auth/token", data=login_data)
    print(f"Login Response Status: {response.status_code}")
    print(f"Login Response Body: {response.json()}")
    return response.json()

if __name__ == "__main__":
    # Test Customer Endpoints
    customer_id, created_customer = create_customer()
    if created_customer and 'version' in created_customer and created_customer['version'] == 1:
        print("Customer creation successful and version initialized to 1.")
    else:
        print("Customer creation failed or version not initialized to 1.")
        
    fetched_customer = get_customer(customer_id)
    if fetched_customer:
        print("Fetched customer details:")
        print(f"  ID Card Images: {type(fetched_customer.get('idCardImages'))} - {fetched_customer.get('idCardImages')}")
        print(f"  Collaterals: {type(fetched_customer.get('collaterals'))} - {fetched_customer.get('collaterals')}")
        print(f"  Documents: {type(fetched_customer.get('documents'))} - {fetched_customer.get('documents')}")
        print(f"  Selected Collateral Types: {type(fetched_customer.get('selectedCollateralTypes'))} - {fetched_customer.get('selectedCollateralTypes')}")
        print(f"  Loan Amount: {fetched_customer.get('loanAmount')}")
        print(f"  Loan Start Date: {fetched_customer.get('loanStartDate')}")
        print(f"  Loan End Date: {fetched_customer.get('loanEndDate')}")
        
    current_version = fetched_customer.get('version') if fetched_customer else 0

    # Test update with incorrect version
    error_response = update_customer_incorrect_version(customer_id, current_version)
    if error_response and error_response.get('errorMessage') and "conflictData" in error_response:
        print("Optimistic locking with incorrect version worked as expected (409 Conflict).")
    else:
        print("Optimistic locking with incorrect version FAILED.")

    # Test update with correct version
    updated_customer_response = update_customer_correct_version(customer_id, current_version)
    if updated_customer_response and updated_customer_response.get('version') == current_version + 1:
        print("Customer update with correct version successful and version incremented.")
        
        # Fetch the customer again to verify last_synced_at from the database
        re_fetched_customer = get_customer(customer_id)
        if re_fetched_customer and re_fetched_customer.get('lastSyncedAt') is not None:
            print(f"Last synced at (after re-fetch): {re_fetched_customer.get('lastSyncedAt')}")
        else:
            print("Last synced at (after re-fetch) is None or customer not found.")
    else:
        print("Customer update with correct version FAILED.")

    # Test Auth Endpoint
    # You might need to create a user in your database for this to work
    # For example, in backend/app/create_db.py, you could add a default user
    login_result = login_user("testuser", "testpassword")
    if login_result and 'userInfo' in login_result:
        user_info = login_result['userInfo']
        print("User login successful. User info fields:")
        print(f"  ID: {user_info.get('id')}")
        print(f"  Username: {user_info.get('username')}")
        print(f"  Email: {user_info.get('email')}")
        print(f"  Role: {user_info.get('role')}")
        print(f"  Department ID: {user_info.get('departmentId')}")
        print(f"  Branch ID: {user_info.get('branchId')}")
    else:
        print("User login FAILED.")